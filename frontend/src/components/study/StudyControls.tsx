import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';
import { 
  ThumbsUp, 
  ThumbsDown, 
  RotateCcw, 
  CheckCircle, 
  XCircle,
  Zap
} from 'lucide-react';

interface StudyControlsProps {
  isCardFlipped: boolean;
  onAnswer: (quality: number) => void;
  onFlipCard: () => void;
  onSkip?: () => void;
  disabled?: boolean;
  className?: string;
}

export function StudyControls({
  isCardFlipped,
  onAnswer,
  onFlipCard,
  onSkip,
  disabled = false,
  className
}: StudyControlsProps) {
  const difficultyButtons = [
    { 
      quality: 1, 
      label: 'Again', 
      icon: XCircle, 
      color: 'bg-red-500 hover:bg-red-600 text-white',
      description: 'Incorrect - show again soon'
    },
    { 
      quality: 2, 
      label: 'Hard', 
      icon: ThumbsDown, 
      color: 'bg-orange-500 hover:bg-orange-600 text-white',
      description: 'Correct but difficult'
    },
    { 
      quality: 3, 
      label: 'Good', 
      icon: CheckCircle, 
      color: 'bg-green-500 hover:bg-green-600 text-white',
      description: 'Correct with some effort'
    },
    { 
      quality: 4, 
      label: 'Easy', 
      icon: ThumbsUp, 
      color: 'bg-blue-500 hover:bg-blue-600 text-white',
      description: 'Correct and easy'
    },
    { 
      quality: 5, 
      label: 'Perfect', 
      icon: Zap, 
      color: 'bg-purple-500 hover:bg-purple-600 text-white',
      description: 'Perfect recall'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        staggerChildren: 0.1
      }
    }
  };

  const buttonVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { type: "spring", stiffness: 300 }
    }
  };

  if (!isCardFlipped) {
    return (
      <motion.div
        className={cn("flex justify-center space-x-4", className)}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={buttonVariants}>
          <Button
            onClick={onFlipCard}
            disabled={disabled}
            size="lg"
            className="px-8 py-3 text-lg font-medium"
            leftIcon={<RotateCcw className="w-5 h-5" />}
          >
            Show Answer
          </Button>
        </motion.div>
        
        {onSkip && (
          <motion.div variants={buttonVariants}>
            <Button
              onClick={onSkip}
              disabled={disabled}
              variant="outline"
              size="lg"
              className="px-6 py-3"
            >
              Skip
            </Button>
          </motion.div>
        )}
      </motion.div>
    );
  }

  return (
    <motion.div
      className={cn("space-y-4", className)}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="text-center mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          How well did you know this?
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Your answer affects when you'll see this card again
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-5 gap-3">
        {difficultyButtons.map((button) => {
          const Icon = button.icon;
          return (
            <motion.div key={button.quality} variants={buttonVariants}>
              <Button
                onClick={() => onAnswer(button.quality)}
                disabled={disabled}
                className={cn(
                  "w-full flex flex-col items-center justify-center p-4 h-auto space-y-2 transition-all duration-200",
                  button.color
                )}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Icon className="w-6 h-6" />
                <span className="font-medium">{button.label}</span>
                <span className="text-xs opacity-90 text-center leading-tight">
                  {button.description}
                </span>
              </Button>
            </motion.div>
          );
        })}
      </div>

      <motion.div 
        className="flex justify-center pt-4"
        variants={buttonVariants}
      >
        <Button
          onClick={onFlipCard}
          disabled={disabled}
          variant="ghost"
          size="sm"
          leftIcon={<RotateCcw className="w-4 h-4" />}
        >
          Flip Back
        </Button>
      </motion.div>
    </motion.div>
  );
}
