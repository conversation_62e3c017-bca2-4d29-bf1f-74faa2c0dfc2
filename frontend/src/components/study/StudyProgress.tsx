import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Progress } from '@/components/ui/Progress';

interface StudyProgressProps {
  currentCard: number;
  totalCards: number;
  correctAnswers: number;
  streak: number;
  sessionTime: number;
  className?: string;
}

export function StudyProgress({
  currentCard,
  totalCards,
  correctAnswers,
  streak,
  sessionTime,
  className
}: StudyProgressProps) {
  const progress = (currentCard / totalCards) * 100;
  const accuracy = currentCard > 0 ? (correctAnswers / currentCard) * 100 : 0;

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Main Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between items-center text-sm">
          <span className="text-gray-600 dark:text-gray-400">
            Progress
          </span>
          <span className="font-medium text-gray-900 dark:text-gray-100">
            {currentCard} / {totalCards}
          </span>
        </div>
        <div className="relative">
          <Progress value={progress} className="h-3" />
          <motion.div
            className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-3 gap-4">
        {/* Accuracy */}
        <motion.div
          className="text-center p-3 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700"
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {Math.round(accuracy)}%
          </div>
          <div className="text-xs text-green-700 dark:text-green-300 font-medium">
            Accuracy
          </div>
        </motion.div>

        {/* Streak */}
        <motion.div
          className="text-center p-3 rounded-lg bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700"
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
            {streak}
          </div>
          <div className="text-xs text-orange-700 dark:text-orange-300 font-medium">
            Streak
          </div>
        </motion.div>

        {/* Time */}
        <motion.div
          className="text-center p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700"
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {formatTime(sessionTime)}
          </div>
          <div className="text-xs text-blue-700 dark:text-blue-300 font-medium">
            Time
          </div>
        </motion.div>
      </div>

      {/* Streak Animation */}
      {streak > 0 && (
        <motion.div
          className="flex items-center justify-center space-x-1"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          {Array.from({ length: Math.min(streak, 5) }).map((_, i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-yellow-400 rounded-full"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: i * 0.1, type: "spring", stiffness: 500 }}
            />
          ))}
          {streak > 5 && (
            <span className="text-sm font-medium text-yellow-600 dark:text-yellow-400 ml-2">
              +{streak - 5}
            </span>
          )}
        </motion.div>
      )}
    </div>
  );
}
