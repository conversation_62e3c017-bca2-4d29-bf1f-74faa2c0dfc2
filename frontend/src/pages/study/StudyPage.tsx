import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { useQuery, useMutation } from "@tanstack/react-query";
import { studyApi, deckApi } from "@/lib/api";
import { StudyCard } from "@/components/study/StudyCard";
import { StudyProgress } from "@/components/study/StudyProgress";
import { StudyControls } from "@/components/study/StudyControls";
import { Button } from "@/components/ui/Button";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";
import { Card } from "@/components/ui/Card";
import { ArrowLeft, Pause, Play, RotateCcw } from "lucide-react";

interface StudySession {
  id: string;
  cards: any[];
  currentIndex: number;
  correctAnswers: number;
  streak: number;
  startTime: Date;
}

export function StudyPage() {
  const { deckId } = useParams();
  const navigate = useNavigate();

  // Study session state
  const [session, setSession] = useState<StudySession | null>(null);
  const [currentCard, setCurrentCard] = useState<any>(null);
  const [isCardFlipped, setIsCardFlipped] = useState(false);
  const [sessionTime, setSessionTime] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [isSessionComplete, setIsSessionComplete] = useState(false);

  // Fetch deck data
  const { data: deck, isLoading: isDeckLoading } = useQuery({
    queryKey: ["deck", deckId],
    queryFn: () => deckApi.getDeck(deckId!),
    enabled: !!deckId,
  });

  // Fetch study cards (try due cards first, then new cards)
  const { data: dueCards, isLoading: isDueCardsLoading } = useQuery({
    queryKey: ["due-cards", deckId],
    queryFn: () => studyApi.getDueCards(deckId!),
    enabled: !!deckId,
  });

  const { data: newCards, isLoading: isNewCardsLoading } = useQuery({
    queryKey: ["new-cards", deckId],
    queryFn: () => studyApi.getNewCards(deckId!),
    enabled:
      !!deckId &&
      (!dueCards?.data?.cards?.length || dueCards?.data?.cards?.length === 0),
  });

  // Combine cards for study
  const studyCards = dueCards?.data?.cards?.length > 0 ? dueCards : newCards;
  const isCardsLoading = isDueCardsLoading || isNewCardsLoading;

  // Create study session mutation
  const createSessionMutation = useMutation({
    mutationFn: studyApi.createSession,
    onSuccess: (data) => {
      console.log("Study session created:", data);
    },
  });

  // Submit card review mutation
  const submitReviewMutation = useMutation({
    mutationFn: studyApi.submitReview,
    onSuccess: () => {
      console.log("Review submitted successfully");
    },
  });

  // Initialize study session
  useEffect(() => {
    if (studyCards?.data && !session) {
      const cards = studyCards.data.cards || studyCards.data;
      if (!cards || cards.length === 0) {
        setIsSessionComplete(true);
        return;
      }

      const newSession: StudySession = {
        id: `session-${Date.now()}`,
        cards,
        currentIndex: 0,
        correctAnswers: 0,
        streak: 0,
        startTime: new Date(),
      };

      setSession(newSession);
      setCurrentCard(cards[0]);

      // Create session in backend
      createSessionMutation.mutate({
        deckId: deckId!,
        sessionType: "review",
      });
    }
  }, [studyCards, session, deckId]);

  // Timer effect
  useEffect(() => {
    if (!session || isPaused || isSessionComplete) return;

    const timer = setInterval(() => {
      setSessionTime((prev) => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [session, isPaused, isSessionComplete]);

  // Handle card flip
  const handleFlipCard = useCallback(() => {
    setIsCardFlipped((prev) => !prev);
  }, []);

  // Handle answer submission
  const handleAnswer = useCallback(
    (quality: number) => {
      if (!session || !currentCard) return;

      const isCorrect = quality >= 3;
      const newCorrectAnswers = session.correctAnswers + (isCorrect ? 1 : 0);
      const newStreak = isCorrect ? session.streak + 1 : 0;

      // Submit review to backend
      submitReviewMutation.mutate({
        cardId: currentCard.id,
        sessionId: session.id,
        quality,
        responseTime: 5000, // TODO: Track actual response time
      });

      // Move to next card
      const nextIndex = session.currentIndex + 1;

      if (nextIndex >= session.cards.length) {
        // Session complete
        setIsSessionComplete(true);
        setSession((prev) =>
          prev
            ? {
                ...prev,
                correctAnswers: newCorrectAnswers,
                streak: newStreak,
              }
            : null
        );
      } else {
        // Update session and move to next card
        const updatedSession = {
          ...session,
          currentIndex: nextIndex,
          correctAnswers: newCorrectAnswers,
          streak: newStreak,
        };

        setSession(updatedSession);
        setCurrentCard(session.cards[nextIndex]);
        setIsCardFlipped(false);
      }
    },
    [session, currentCard, submitReviewMutation]
  );

  // Handle session restart
  const handleRestart = useCallback(() => {
    if (!session) return;

    setSession({
      ...session,
      currentIndex: 0,
      correctAnswers: 0,
      streak: 0,
      startTime: new Date(),
    });
    setCurrentCard(session.cards[0]);
    setIsCardFlipped(false);
    setSessionTime(0);
    setIsSessionComplete(false);
  }, [session]);

  // Loading state
  if (isDeckLoading || isCardsLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // No cards to study
  if (isSessionComplete && session?.cards.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <Card className="p-8 text-center max-w-md">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            No Cards to Study
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            All cards in this deck are up to date. Great job!
          </p>
          <Button
            onClick={() => navigate("/decks")}
            leftIcon={<ArrowLeft className="w-4 h-4" />}
          >
            Back to Decks
          </Button>
        </Card>
      </div>
    );
  }

  // Session complete
  if (isSessionComplete && session) {
    const accuracy =
      session.cards.length > 0
        ? (session.correctAnswers / session.cards.length) * 100
        : 0;

    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="p-8 text-center max-w-lg">
            <motion.div
              initial={{ y: -20 }}
              animate={{ y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                Session Complete! 🎉
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mb-8">
                Great job studying {deck?.data?.title}
              </p>
            </motion.div>

            <motion.div
              className="grid grid-cols-3 gap-6 mb-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                  {session.cards.length}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Cards Studied
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                  {Math.round(accuracy)}%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Accuracy
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                  {Math.floor(sessionTime / 60)}:
                  {(sessionTime % 60).toString().padStart(2, "0")}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Time
                </div>
              </div>
            </motion.div>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6 }}
            >
              <Button
                onClick={handleRestart}
                leftIcon={<RotateCcw className="w-4 h-4" />}
                size="lg"
              >
                Study Again
              </Button>
              <Button
                onClick={() => navigate("/decks")}
                variant="outline"
                leftIcon={<ArrowLeft className="w-4 h-4" />}
                size="lg"
              >
                Back to Decks
              </Button>
            </motion.div>
          </Card>
        </motion.div>
      </div>
    );
  }

  // Main study interface
  if (!session || !currentCard) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <motion.div
        className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-4"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => navigate("/decks")}
              variant="ghost"
              size="sm"
              leftIcon={<ArrowLeft className="w-4 h-4" />}
            >
              Back
            </Button>
            <div>
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                {deck?.data?.title}
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Study Session
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              onClick={() => setIsPaused(!isPaused)}
              variant="ghost"
              size="sm"
              leftIcon={
                isPaused ? (
                  <Play className="w-4 h-4" />
                ) : (
                  <Pause className="w-4 h-4" />
                )
              }
            >
              {isPaused ? "Resume" : "Pause"}
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto p-4 space-y-6">
        {/* Progress */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <StudyProgress
            currentCard={session.currentIndex + 1}
            totalCards={session.cards.length}
            correctAnswers={session.correctAnswers}
            streak={session.streak}
            sessionTime={sessionTime}
          />
        </motion.div>

        {/* Study Card */}
        <motion.div
          className="flex justify-center"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
        >
          <div className="w-full max-w-2xl">
            <AnimatePresence mode="wait">
              <StudyCard
                key={currentCard.id}
                frontContent={currentCard.frontContent}
                backContent={currentCard.backContent}
                isFlipped={isCardFlipped}
                onFlip={handleFlipCard}
              />
            </AnimatePresence>
          </div>
        </motion.div>

        {/* Controls */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <StudyControls
            isCardFlipped={isCardFlipped}
            onAnswer={handleAnswer}
            onFlipCard={handleFlipCard}
            disabled={isPaused || submitReviewMutation.isPending}
          />
        </motion.div>
      </div>
    </div>
  );
}
